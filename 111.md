00:48:16.474/V: 开始运行 [$remote/xhs root直接跳转服务端版.js].
00:48:16.483/D: 小红书API模块已加载
00:48:16.484/D: AutoJS环境检测到，启动主函数...
00:48:16.484/D: === 小红书自动互动工具 - 服务端版本 ===
00:48:16.484/D: 正在初始化...
00:48:16.484/D: 当前使用Root Shell模式
00:48:16.484/D: 首次尝试获取root权限...
00:48:16.511/D: 成功获取root权限，标记为已授权
00:48:16.512/D: 正在进行设备注册...
00:48:16.512/D: 生成新的随机设备令牌
00:48:16.516/D: 新生成的设备令牌: device_1753807696512_6fi9hqfg
00:48:16.517/D: 发送设备注册请求: {"username":"admin","device_token":"device_1753807696512_6fi9hqfg","device_name":"samsung SM-N976N","device_type":"mobile","is_temp":false}
00:48:16.517/D: 注册路径: http://xhss.ke999.cn/device/register
00:48:16.602/D: 注册响应状态码: 200
00:48:16.604/D: 设备注册结果: {"success":true,"message":"设备注册成功","data":{"device_name":"samsung SM-N976N","operation_count":0,"max_operations":2,"remaining_operations":2,"configs":[{"id":2,"user_id":1,"name":"操作几次恢复出厂设置","config":{"阅读开关":1,"操作几次恢复出厂设置":4},"description":"还原几次备份操作后恢复出厂设置","is_default":1,"created_at":"2025-07-04T10:28:17.000Z","updated_at":"2025-07-29T16:25:45.000Z"}]}}
00:48:16.606/D: 已保存默认配置: 阅读开关
00:48:16.606/D: 已保存默认配置: 操作几次恢复出厂设置
00:48:16.607/D: 已设置全局变量: 操作几次恢复出厂设置 = 4
00:48:16.610/D: 从默认配置设置全局变量: 操作几次恢复出厂设置 = 4
00:48:16.611/D: 从默认配置设置全局变量: 阅读开关 = 1
00:48:16.611/D: 设备注册成功，当前设备令牌: device_1753807696512_6fi9hqfg
00:48:16.611/D: 当前操作信息: {"操作次数":0,"最大操作次数":2,"剩余操作次数":2}
00:48:16.611/D: 设备注册成功，开始获取操作信息...
00:48:16.611/D: 当前操作信息: {"操作次数":0,"最大操作次数":2,"剩余操作次数":2}
00:48:16.611/D: 开始循环操作，从服务端获取最大操作次数: 2 次
00:48:16.617/D: 启动小红书应用...
00:48:16.618/D: 启动小红书应用
00:48:16.640/D: 小红书启动成功，等待应用加载...
00:48:16.640/D: 等待 50 毫秒
00:48:16.739/D: 小红书应用已成功启动并处于前台
00:48:16.740/D: [1/2] 获取链接中...
00:48:16.740/D: 发送GET请求到: http://xhss.ke999.cn/next-link
00:48:16.740/D: 请求数据: {"username":"admin","device_token":"device_1753807696512_6fi9hqfg"}
00:48:16.743/D: 完整GET请求URL: http://xhss.ke999.cn/next-link?username=admin&device_token=device_1753807696512_6fi9hqfg
00:48:16.826/D: 响应状态码: 200
00:48:16.827/D: 响应内容: {"success":true,"message":"获取链接成功","status":"success","data":{"link_id":171,"url":"http://xhslink.com/a/mlMsTydokzMfb","task_id":44,"original_likes":148,"current_likes":148,"target_likes":8,"actual_like_increase":0,"like_operations":2,"like_count":2,"original_collects":1554,"current_collects":1554,"target_collects":2,"actual_collect_increase":0,"collect_operations":0,"collect_count":0,"is_completed":false,"operation_count":0,"max_operations":2,"remaining_operations":2}}
00:48:16.827/D: 获取链接结果: {"success":true,"message":"获取链接成功","status":"success","data":{"link_id":171,"url":"http://xhslink.com/a/mlMsTydokzMfb","task_id":44,"original_likes":148,"current_likes":148,"target_likes":8,"actual_like_increase":0,"like_operations":2,"like_count":2,"original_collects":1554,"current_collects":1554,"target_collects":2,"actual_collect_increase":0,"collect_operations":0,"collect_count":0,"is_completed":false,"operation_count":0,"max_operations":2,"remaining_operations":2}}
00:48:16.828/D: 保存链接ID: 171
00:48:16.829/D: 获取到链接: http://xhslink.com/a/mlMsTydokzMfb
00:48:16.829/D: 链接ID: 171
00:48:16.829/D: 目标数量 - 点赞: 8, 收藏: 2
00:48:16.829/D: 原始数量 - 点赞: 148, 收藏: 1554
00:48:16.836/D: 任务类型: both, 目标点赞数: 8, 目标收藏数: 2
00:48:16.842/D: 实际需要: 点赞=true, 收藏=true
00:48:16.861/D: 获取到当前配置: 操作几次恢复出厂设置
00:48:16.873/D: 使用执行自动互动函数处理链接...
00:48:16.874/D: 执行自动互动操作: http://xhslink.com/a/mlMsTydokzMfb
00:48:16.875/D: 配置: 点赞=true, 收藏=true, 浏览延时=3秒
00:48:16.876/D: 目标数量: 点赞=8, 收藏=2
00:48:16.876/D: 原始数量: 点赞=148, 收藏=1554
00:48:16.877/D: 直接互动链接文章: http://xhslink.com/a/mlMsTydokzMfb
00:48:16.877/D: 操作选项: 点赞=true, 收藏=true, 浏览延时=3秒
00:48:16.882/D: 目标数量: 点赞=8, 收藏=2
00:48:16.883/D: 原始数量: 点赞=148, 收藏=1554
00:48:16.883/D: 等待 1000 毫秒
00:48:17.885/D: 已打开链接
00:48:17.885/D: 准备打开小红书链接: http://xhslink.com/a/mlMsTydokzMfb
00:48:17.885/D: URL类型: string
00:48:17.886/D: URL长度: 34
00:48:17.886/D: 检测到小红书短链接，开始解析...
00:48:17.886/D: 开始多策略解析短链接: http://xhslink.com/a/mlMsTydokzMfb
00:48:17.886/D: 原始URL: http://xhslink.com/a/mlMsTydokzMfb
00:48:17.887/D: URL字符编码检查: "http://xhslink.com/a/mlMsTydokzMfb"
00:48:17.887/D: 处理后的URL: http://xhslink.com/a/mlMsTydokzMfb
00:48:17.887/D: URL验证通过: http://xhslink.com/a/mlMsTydokzMfb
00:48:17.888/D: 尝试User-Agent 1/3
00:48:17.888/D: 请求URL: http://xhslink.com/a/mlMsTydokzMfb
00:48:17.889/D: URL类型: string
00:48:17.889/D: URL长度: 34
00:48:18.182/D: 响应状态码: 200
00:48:18.324/D: 尝试User-Agent 2/3
00:48:18.325/D: 请求URL: http://xhslink.com/a/mlMsTydokzMfb
00:48:18.325/D: URL类型: string
00:48:18.326/D: URL长度: 34
00:48:18.472/D: 响应状态码: 200
00:48:18.559/D: 尝试User-Agent 3/3
00:48:18.560/D: 请求URL: http://xhslink.com/a/mlMsTydokzMfb
00:48:18.560/D: URL类型: string
00:48:18.560/D: URL长度: 34
00:48:18.721/D: 响应状态码: 200
00:48:18.810/D: 尝试跟随重定向...
00:48:18.810/D: 跟随重定向URL: http://xhslink.com/a/mlMsTydokzMfb
00:48:18.955/D: 跟随重定向后的URL: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
00:48:18.955/D: 短链接解析成功: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
00:48:18.956/D: 准备打开小红书链接: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
00:48:18.956/D: URL类型: string
00:48:18.956/D: URL长度: 409
00:48:18.956/D: 检测到小红书长链接，提取ID
00:48:18.956/D: URL类型: string
00:48:18.956/D: URL内容: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
00:48:18.956/D: 提取笔记ID - 输入URL: https://www.xiaohongshu.com/discovery/item/641c1b1a0000000027013c58?app_platform=android&ignoreEngage=true&app_version=8.88.0&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBC1NmTCQmp0XCPuO8bUBkf2WziUTJv6a-ChcUBI_PzuI%3D&author_share=1&xhsshare=CopyLink&shareRedId=N0xGOEdHRTo2NzUyOTgwNjY0OTc8S0hB&apptime=1750775904&share_id=f75c8dade0da4f2999c3e48e80b274c1&share_channel=copy_link
00:48:18.956/D: 通过/discovery/item/路径提取到ID: 641c1b1a0000000027013c58
00:48:18.956/D: 从长链接提取到笔记ID: 641c1b1a0000000027013c58
00:48:18.957/D: 使用ID打开小红书: 641c1b1a0000000027013c58
00:48:18.979/D: 已发送打开请求
00:48:18.979/D: 等待 2000 毫秒
00:48:20.980/D: 开始处理打开流程，最大尝试次数: 50
00:48:20.981/D: 当前尝试次数: 1
00:48:20.981/D: 执行Root命令: dumpsys window | grep mCurrentFocus
00:48:21.031/D: 命令执行成功
00:48:21.031/D: 当前应用包名: com.xingin.xhs
00:48:21.031/D: 已检测到进入小红书应用
00:48:21.031/D: 等待 2000 毫秒
00:48:23.033/D: 获取页面信息，检查点赞状态和内容类型...
00:48:23.033/D: 开始获取界面 XML...
00:48:23.034/D: 执行Root命令: uiautomator dump /sdcard/window_dump.xml
00:48:24.342/D: 命令执行成功
00:48:24.342/D: 界面 XML 导出成功
00:48:24.343/D: 成功读取 XML 文件，大小: 19437 字节
00:48:24.343/D: <?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.RelativeLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1080,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.view.ViewGroup" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1770]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1516]"><node index="0" text="" resource-id="com.xingin.xhs:id/noteContentLayout" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1516]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="图片,第1张,共1张,双指左划或右划即可查看更多内容" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1492]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1492]" /><node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="androidx.recyclerview.widget.RecyclerView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1492]"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1492]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1492]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,1492]" /></node></node></node><node index="2" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,240][1080,540]" /></node></node></node><node index="1" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1516][1080,1770]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[3,1516][1077,1770]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[3,1531][1077,1605]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[3,1531][1077,1605]"><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[3,1531][1077,1605]"><node index="0" text="细思极恐系列1" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[48,1546][1032,1605]" /></node></node></node><node index="1" text="" resource-id="" class="android.view.ViewGroup" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,1650][1032,1725]"><node index="0" text="2023-03-23" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[48,1669][819,1707]" /></node></node></node></node></node></node><node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,72][1080,240]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,72][153,240]"><node NAF="true" index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[45,102][153,210]" /></node><node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[153,72][1080,240]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[153,72][1080,240]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.RelativeLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[153,72][1080,240]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[153,72][333,240]"><node index="0" text="" resource-id="com.xingin.xhs:id/avatarLayout" class="android.view.View" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[153,72][333,240]" /></node><node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.Button" package="com.xingin.xhs" content-desc="作者,一闪亮晶晶女装" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[333,130][708,181]"><node index="0" text="一闪亮晶晶女装" resource-id="com.xingin.xhs:id/nickNameTV" class="android.widget.TextView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[333,130][648,181]" /></node><node index="2" text="" resource-id="" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[738,114][927,198]"><node index="0" text="关注" resource-id="" class="android.widget.TextView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[738,114][927,198]" /></node><node index="3" text="" resource-id="com.xingin.xhs:id/moreOperateIV" class="android.widget.Button" package="com.xingin.xhs" content-desc="分享" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[927,90][1059,222]" /></node></node></node></node><node index="4" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.LinearLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1752][1080,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.FrameLayout" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1752][1080,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.view.ViewGroup" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,1752][1080,1920]"><node index="0" text="说点什么..." resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.xingin.xhs" content-desc="评论框" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[45,1782][510,1890]" /><node index="1" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.Button" package="com.xingin.xhs" content-desc="已点赞 148" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[546,1752][708,1920]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[546,1752][708,1914]" /><node index="1" text="148" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[645,1811][708,1860]" /></node><node index="2" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.Button" package="com.xingin.xhs" content-desc="收藏 1554" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[732,1776][915,1896]"><node index="0" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.ImageView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[732,1791][822,1881]" /><node index="1" text="1554" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[831,1811][915,1860]" /></node><node index="3" text="" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.Button" package="com.xingin.xhs" content-desc="评论 1" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[915,1776][1035,1896]"><node index="0" text="" resource-id="" class="android.widget.ImageView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[915,1791][1005,1881]" /><node index="1" text="1" resource-id="com.xingin.xhs:id/0_resource_name_obfuscated" class="android.widget.TextView" package="com.xingin.xhs" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1014,1811][1035,1860]" /></node></node></node></node></node></node></node></node></node></node></node></node></hierarchy>
00:48:24.344/D: XML获取成功，开始解析页面元素...
00:48:24.344/D: 开始解析 XML 中的文本元素...
00:48:24.355/D: 共找到 12 个有效元素 (已过滤 46 个空文本元素)
00:48:24.361/D: 图文用户名: [一闪亮晶晶女装], 坐标: (490, 155)
00:48:24.362/D: 点赞数: [148], 坐标: (627, 1836)
00:48:24.362/D: 收藏数: [1554], 坐标: (823, 1836)
00:48:24.362/D: 评论数: [0], 坐标: (277, 1836)
00:48:24.362/D: 内容类型: 图文
00:48:24.362/D: 未检测到分享按钮，使用左侧X偏移检测方式（图文界面）
00:48:24.363/D: 页面元素解析完成，后续操作将复用此数据，不再重复获取XML
00:48:24.363/D: 当前页面数量: 点赞=148, 收藏=1554
00:48:24.363/D: 开始判断点赞状态
00:48:24.363/D: 通过content-desc判断点赞状态: 已点赞
00:48:24.363/D: 当前点赞状态: 已点赞
00:48:24.364/D: 开始判断收藏状态
00:48:24.364/D: 通过content-desc判断收藏状态: 未收藏
00:48:24.364/D: 当前收藏状态: 未收藏
00:48:24.364/D: 点赞达标检查: 当前增长=0, 目标=8, 已达标=false
00:48:24.364/D: 收藏达标检查: 当前增长=0, 目标=2, 已达标=false
00:48:24.364/D: === 开始精准判断操作必要性 ===
00:48:24.364/D: ✓ 点赞任务：已完成（当前状态：已点赞）
00:48:24.364/D: ○ 收藏任务：需要执行（当前状态：未收藏，且未达标）
00:48:24.364/D: === 判断结果：需要执行操作 - 点赞:否, 收藏:是 ===
00:48:24.365/D: 页面类型: 图文
00:48:24.365/D: 阅读开关状态: 1 (1=开启浏览延时, 其他=跳过浏览延时)
00:48:24.365/D: 阅读开关已开启，开始浏览延时 3 秒...
00:48:24.365/D: 图文内容，浏览延时 3 秒期间进行滑动操作
00:48:24.366/D: 前期等待 1 秒
00:48:24.366/D: 等待 1000 毫秒
00:48:25.369/D: 开始滑动浏览图文内容
00:48:25.369/D: 执行滑动操作: (638.971024798736, 1723.9395586452185) -> (638.971024798736, 695.9110620966088), 时长: 317ms
00:48:25.369/D: 执行Root命令: input swipe 638.971024798736 1723.9395586452185 638.971024798736 695.9110620966088 317
00:48:25.830/D: 命令执行成功
00:48:25.830/D: 滑动操作成功
00:48:25.830/D: 滑动完成，继续等待 2 秒完成浏览延时
00:48:25.830/D: 等待 2000 毫秒
00:48:27.831/D: 浏览延时完成，下滑回顶部准备点赞操作
00:48:27.832/D: 执行滑动操作: (540, 576) -> (540, 1536), 时长: 500ms
00:48:27.832/D: 执行Root命令: input swipe 540 576 540 1536 500
00:48:28.496/D: 命令执行成功
00:48:28.496/D: 滑动操作成功
00:48:28.496/D: 等待 1000 毫秒
00:48:29.497/D: 浏览延时完成，开始执行互动操作
00:48:29.497/D: 浏览完成，开始执行互动操作
00:48:29.497/D: 跳过点赞操作（任务已完成或不需要点赞）
00:48:29.497/D: 开始执行收藏操作
00:48:29.498/D: 等待 1000 毫秒
00:48:30.498/D: 图文界面：点击收藏数左侧X偏移位置 (657, 1836)
00:48:30.499/D: 执行点击操作: (657, 1836)
00:48:30.499/D: 执行Root命令: input tap 657 1836
00:48:30.614/D: 命令执行成功
00:48:30.614/D: 点击操作成功
00:48:30.614/D: 等待 1000 毫秒
00:48:31.615/D: 收藏操作完成
00:48:31.615/D: 所有互动操作完成
00:48:31.615/D: 操作结果: 已点赞，跳过, 收藏成功
00:48:31.615/D: 提交到服务端的操作前数量: 点赞=148, 收藏=1554
00:48:31.616/D: 发送POST请求到: http://xhss.ke999.cn/update-status
00:48:31.616/D: 请求数据: {"username":"admin","device_token":"device_1753807696512_6fi9hqfg","link_id":171,"status":"success","operation_type":"both","before_like_count":148,"before_collect_count":1554}
00:48:31.684/D: 响应状态码: 200
00:48:31.686/D: 响应内容: {"success":true,"message":"操作成功","data":{"link_id":"171","original_likes":148,"current_likes":148,"target_likes":8,"actual_like_increase":0,"like_operations":3,"like_count":3,"original_collects":1554,"current_collects":1554,"target_collects":2,"actual_collect_increase":0,"collect_operations":1,"collect_count":1,"is_completed":false,"status":"active"}}
00:48:31.686/D: 更新状态结果: {"success":true,"message":"操作成功","data":{"link_id":"171","original_likes":148,"current_likes":148,"target_likes":8,"actual_like_increase":0,"like_operations":3,"like_count":3,"original_collects":1554,"current_collects":1554,"target_collects":2,"actual_collect_increase":0,"collect_operations":1,"collect_count":1,"is_completed":false,"status":"active"}}
00:48:31.686/D: 服务端返回操作信息 - 操作次数: 0, 最大操作次数: 0, 剩余操作次数: 0
00:48:31.686/D: 操作成功: 已点赞，跳过, 收藏成功
00:48:31.687/D: 执行了新操作，需要操作间隔 5 秒
00:48:31.687/D: 等待 5 秒后处理下一条链接
00:48:32.688/D: 等待 4 秒后处理下一条链接
00:48:33.688/D: 等待 3 秒后处理下一条链接
00:48:34.690/D: 等待 2 秒后处理下一条链接
00:48:35.691/D: 等待 1 秒后处理下一条链接
00:48:36.692/D: 执行返回操作
00:48:36.692/D: 执行Root命令: input keyevent 4
00:48:36.841/D: 命令执行成功
00:48:36.842/D: 返回操作成功
00:48:36.842/D: [2/2] 获取链接中...
00:48:36.842/D: 发送GET请求到: http://xhss.ke999.cn/next-link
00:48:36.842/D: 请求数据: {"username":"admin","device_token":"device_1753807696512_6fi9hqfg"}
00:48:36.842/D: 完整GET请求URL: http://xhss.ke999.cn/next-link?username=admin&device_token=device_1753807696512_6fi9hqfg
00:48:36.887/D: 响应状态码: 200
00:48:36.889/D: 响应内容: {"success":true,"message":"今日操作次数已达上限","status":"limit_reached","data":{"operation_count":2,"max_operations":2}}
00:48:36.889/D: 获取链接结果: {"success":true,"message":"今日操作次数已达上限","status":"limit_reached","data":{"operation_count":2,"max_operations":2}}
00:48:36.889/D: 达到每日操作上限，准备退出脚本
00:48:39.914/V: [$remote/xhs root直接跳转服务端版.js] 运行结束 (用时 23.433 秒)

00:48:39.919/D: 脚本即将退出，执行清理工作...
00:48:39.920/D: 清理工作完成
